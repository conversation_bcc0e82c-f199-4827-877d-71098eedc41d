
## 总体任务

实现一个 python 脚本，按照以下逻辑筛选股票：
第一步，获取热门股票列表，后面有参考代码；
第二步，从热门股票列表里按如下策略筛选，获取k线详情代码参见后面例子：
```
## 策略说明

策略名称：单阳不破
单阳不破是一种强势的短线信号。

## 策略涉及指标

1. 定义 N：统计 K 线周期，缺省为 10，如最近 9日统计周期，则 N=9
2. 定义 T：出现事件的交易日
3. 定义 VOL(T)，T日当日的成交量
4. 定义 HSL(T)，T日当日的换手率

## 筛选条件

1. 最近 N 个交易日内出现涨幅 > 7% 的大阳线，记出现此涨幅的日期为 T 日
2. 最近 N 个交易日内，仅出现 1 次满足条件 1 的情况
3. VOL(T) > 2 * MA5 （此处MA5 为T日前五日平均成交量）
4. T+1，T+2日，温和放量。温和放量指当日成交量介于1.2 * VOL(T)与1.5*VOL(T)之间 
5. T+3日及以后，若当日收阴线，则要求当日成交量 < 0.7 * VOL(T)，若收阳线，则要求当日成交量 > 0.5 * VOL(T)
```
第三步：汇总打印股票列表。

## 技术栈
语言：python, 使用虚拟环境安装依赖
虚拟环境目录：venv


### 获取热门股票（仅用于获取股票代码）

参考代码：
```
# 获取热门股票列表函数
def load_hot_stocks(pages=20):
    base_url = "https://vip.stock.finance.sina.com.cn/quotes_service/api/json_v2.php/Market_Center.getHQNodeData"
    params = {
        'num': 80,
        'sort': 'turnoverratio',
        'asc': 0,
        'node': 'hs_a',
        '_s_r_a': 'setlen'
    }
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.162 Safari/537.36'
    }

    hot_stocks = []

    for page in range(1, pages + 1):
        params['page'] = page
        response = requests.get(base_url, params=params, headers=headers)
        
        if response.status_code == 200 and response.text:
            try:
                data = json.loads(response.text)
                for item in data:
                    if int(item.get('amount', 0)) > 50000000 and float(item.get('changepercent', 0)) < 5.0:
                        stock = {
                            'code': item['symbol'],
                            'name': item['name'],
                            'volume': int(item['volume']),
                            'price': float(item['trade']),
                            'percent': float(item['changepercent']),
                            'turnoverratio': float(item['turnoverratio'])
                        }
                        hot_stocks.append(stock)
            except json.JSONDecodeError as e:
                print(f"Failed to parse JSON: {e}")
    
            # 随机停顿一段时间（最多2秒）
        delay = random.uniform(0, 2)
        time.sleep(delay)
                
    return hot_stocks
```

股票代码说明：
SH：上海交易所
SZ：深圳交易所
BJ：北京交易所

## 获取K线详情（用于获取K线详情）

参考代码：
```
XQ_COOKIE = 'cookiesu=511750991532166; device_id=cd7a4d660774a60c4265c292c3283d3c; s=c11ac7hq0t; xq_a_token=a37340bcacde3256b44014765282ee5cbc7ad60e; xqat=a37340bcacde3256b44014765282ee5cbc7ad60e; xq_id_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.eyJ1aWQiOjcxNjY4NzUzMTgsImlzcyI6InVjIiwiZXhwIjoxNzUzOTQyNTg4LCJjdG0iOjE3NTEzNTA1ODg4ODAsImNpZCI6ImQ5ZDBuNEFadXAifQ.C9xmeCx9ERUIzMUeSngEhzEWTBCc0hDaDRlC6eE7Xjs6xlhbTvKXUdht2lpIF-mgCUbqLy3wUuytyx_qd3ZEhZ0pf4QsE4lOcKdUUCHbK8eYgchz3m11LgnLD9enqX9marakAPue8lr95EYbNSCwIHuyIDKxHprU2XQQ3WKAWKWZVOALjgnNDk2shf2LLjmIKX44D2dgSiT_ghPM_4Y7Gazn43ivAwdcovvXMVub3uQ_eD4VUjy1znRNi6Ndy-EeRik5zMwwswyJtsEgqe7mnAEZQaSoKWHDNGYpfud64me-wxBJUagA-NHXLuWNT7RTVAIxDdSZtm8lxIREFDLekA; xq_r_token=ce62c023f0f711294564f749d8d43a25e749d876; xq_is_login=1; u=7166875318; ssxmod_itna=iq+x9DcQ0QqCTxeKxmwd4QkP1D73DzxC5iOD+xQ5DODLxn4GQDUjYqGw3Yhq4j2+x41DSGDY7qKAqDseiD4GzDiqmLx9SAuG5etoneIq3Khh0ivIorqkyH40iUnyRcIrTi6l9=klCxYD834DKqGmD0=DAweD7qepDYYDC4GwDGoD34DiDDPeHmr=rCiODGPrajrtxi3DbrWDmqGnihTDA40HxD3KxAtWnmb7wDDBmb9xxKGvYImqXWTQNeGumZibtpOvhzDOYAcpAeGyes8KoxabMluKTHIFN8DgW3d7jxeeBwDkA7GhQio/kD0Axs05SG4YGY0AqWi55xeOodS3+aeKNSDG7CpMCUqeydtd7xtitd2D97oNEd10qjWYG7xyk5ZB5lAs3nhYG4WDGtAQHjzWiDD; ssxmod_itna2=iq+x9DcQ0QqCTxeKxmwd4QkP1D73DzxC5iOD+xQ5DODLxn4GQDUjYqGw3Yhq4j2+x41DSGDY7qKD4DWGSDRoW4eFixDsMtKZg4kSBgkxN3qiW1dSt4D'

# 获取日K线数据并进行分析
def get_kline_data(code, start_date, count=400, period='day', XQ_COOKIE=''):
    url = f"https://stock.xueqiu.com/v5/stock/chart/kline.json?symbol={code.upper()}&begin={start_date}&period={period}&type=before&count=-{count}&indicator=kline"

    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.162 Safari/537.36',
        #device_id=99c4d5d7a3335d4058299e6f7c063137; cookiesu=***************; bid=32ecdcf76d4a1cac98d0086a64d13664_m2fl9qpj; s=aq1jot85f1; xq_is_login=1; u=**********; Hm_lvt_1db88642e346389874251b5a1eded6e3=**********,**********; HMACCOUNT=194717C780973511; snbim_minify=true; xq_a_token=1f096f1241d7bcc9fe7fd96f2d98b3ee39e27816; xqat=1f096f1241d7bcc9fe7fd96f2d98b3ee39e27816; xq_id_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.******************************************************************************************************************.N5YoQJ4ja8WQuQwzNmSYqqiguqzzwqz8jqY4q20xWjVOGlzsh-SmzFPcob8qL5S3VPtrPldqeHQxhQHX5r-f6AHLpsRroS0RmsxyZlURvYJ9CMmMbyalUsAyH1-v0xO5x9nx2C3PAWG-Vzta9k-0UFA6Xtd-YMhQlTnPjwGYrxMgX2RWLJz1t5hDoR4PumehtXMWmCt6MJPqWQMtyXgEB4J6whd8HNSqCtyRNgV1AZ8ies0bhc3PeFQwTbEbM1rNzS9l_th2SEJZxnAdY6utj5CWojwDoCOzyHTm5E6EuRhrsRkrLuqwOBzsESjwmK5BJbyEZB3AsWDLhGr_KX6YBQ; xq_r_token=7e8c801b703a415d67f233e054dfd6eef193eff1; is_overseas=0; Hm_lpvt_1db88642e346389874251b5a1eded6e3=**********; ssxmod_itna=QqAx9DRGiti=DQd0=DXiDHzHePewCAr4DvP4vUUxLtND/S8ADnqD=GFDK40ofk+rNjlDxmK=CxOcvPYGcx2q7mCt2GdeqCojPGIDeKG2DmeDyDi5GRD0KPqWDentD5xGoDPxDeDA7FZ8D7oNgjTXUbMjXFdNDm36RDGjnDit3xivllw6Z7eGWRqGfDDoDY3XgIldDGLKDbdQDIdNqKBxxx6Q7a=608PDu8==YuqDLAe9FxB6RSnpgbtODtk6nRjj0UGN=HLTdYDd4AiYeKh4mWEwbYBhxQYCRk5xoFvCdRfGjtElFefsDG31F0q4D===; ssxmod_itna2=QqAx9DRGiti=DQd0=DXiDHzHePewCAr4DvP4vUUxLtG98oSQzDBTZ2D7Pq4mlBOnzmkY4ocDn+megi+7mXtN8iYOfPeu=Dx=m=KcmG9t1MD9E9xp5+zG8m+r9rdHB=5iXLmI/CyPXCgh9iqiKbGoFYph57k6FtMgACuPgk+cyku=yF6D071nGYw7Trbnve+xfA9QvrvI3xeD08DY9q4D
        'Cookie': XQ_COOKIE
    }
    

    response = requests.get(url, headers=headers)
```

请求例子：
https://stock.xueqiu.com/v5/stock/chart/kline.json?symbol=SZ300083&begin=1752721474588&period=day&type=before&count=-90&indicator=kline

参数解释：
begin：当前时间戳
symbol：大写的股票代码，如 SZ000333
period：周期，day，week
count：回溯天数，-90 为过去 90天，如果获取 800根k线就是 count=-800

响应结构为：
```
{"data":{"symbol":"SZ300083","column":["timestamp","volume","open","high","low","close","chg","percent","turnoverrate","amount","volume_post","amount_post"],"item":[[1752076800000,31485597,8.23,8.3,8.15,8.2,0.0,0.0,2.11,2.5850056E8,6000,49200.0],[1752163200000,43501870,8.19,8.33,8.14,8.28,0.08,0.98,2.91,3.58488868E8,3600,29808.0],[1752422400000,51128085,8.28,8.42,8.27,8.38,0.1,1.21,3.42,4.27619577E8,44400,372072.0],[1752508800000,59225437,8.39,8.53,8.25,8.37,-0.01,-0.12,3.97,4.95832186E8,1300,10881.0],[1752595200000,27728200,8.35,8.46,8.31,8.43,0.06,0.72,1.86,2.32831893E8,null,null]]},"error_code":0,"error_description":""}
```


